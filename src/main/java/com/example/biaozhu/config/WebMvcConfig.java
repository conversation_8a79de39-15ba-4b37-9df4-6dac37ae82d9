package com.example.biaozhu.config;

import org.springframework.context.annotation.Configuration;
import org.springframework.web.servlet.config.annotation.ResourceHandlerRegistry;
import org.springframework.web.servlet.config.annotation.WebMvcConfigurer;
import java.io.File;

@Configuration
public class WebMvcConfig implements WebMvcConfigurer {

    @Override
    public void addResourceHandlers(ResourceHandlerRegistry registry) {
        // 添加上传文件目录映射
        // 使用绝对路径确保正确映射
        String uploadPath = System.getProperty("user.dir") + File.separator + "uploads" + File.separator;

        // 确保路径格式正确
        String fileUrl = "file:" + uploadPath.replace("\\", "/");

        registry.addResourceHandler("/uploads/**")
                .addResourceLocations(fileUrl);

        System.out.println("静态资源映射配置: /uploads/** -> " + fileUrl);
        System.out.println("实际文件路径: " + uploadPath);

        // 检查uploads目录是否存在
        File uploadsDir = new File(uploadPath);
        if (uploadsDir.exists()) {
            System.out.println("uploads目录存在: " + uploadsDir.getAbsolutePath());
        } else {
            System.out.println("uploads目录不存在: " + uploadsDir.getAbsolutePath());
        }
    }
} 