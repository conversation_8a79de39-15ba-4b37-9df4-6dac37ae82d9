package com.example.biaozhu.controller;

import org.springframework.http.ResponseEntity;
import org.springframework.web.bind.annotation.*;

import java.io.File;
import java.nio.file.Files;
import java.nio.file.Path;
import java.nio.file.Paths;
import java.util.HashMap;
import java.util.Map;

/**
 * 文件访问测试控制器
 */
@RestController
@RequestMapping("/api/file-access")
public class FileAccessController {

    /**
     * 测试文件是否存在
     */
    @GetMapping("/test/{filename}")
    public ResponseEntity<?> testFileAccess(@PathVariable String filename) {
        Map<String, Object> response = new HashMap<>();
        
        try {
            // 检查文件是否存在
            String uploadDir = "uploads/dataset-1";
            Path filePath = Paths.get(uploadDir, filename);
            File file = filePath.toFile();
            
            response.put("filename", filename);
            response.put("requestedPath", filePath.toString());
            response.put("absolutePath", file.getAbsolutePath());
            response.put("exists", file.exists());
            response.put("canRead", file.canRead());
            response.put("size", file.exists() ? file.length() : 0);
            
            if (file.exists()) {
                response.put("status", "success");
                response.put("message", "文件存在且可访问");
                response.put("accessUrl", "/uploads/dataset-1/" + filename);
            } else {
                response.put("status", "error");
                response.put("message", "文件不存在");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "检查文件时出错: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    /**
     * 列出uploads目录下的所有文件
     */
    @GetMapping("/list")
    public ResponseEntity<?> listFiles() {
        Map<String, Object> response = new HashMap<>();
        
        try {
            String uploadDir = "uploads";
            File uploadsFolder = new File(uploadDir);
            
            response.put("uploadsDir", uploadsFolder.getAbsolutePath());
            response.put("exists", uploadsFolder.exists());
            
            if (uploadsFolder.exists()) {
                listDirectoryContents(uploadsFolder, response, "");
            }
            
            return ResponseEntity.ok(response);
        } catch (Exception e) {
            response.put("status", "error");
            response.put("message", "列出文件时出错: " + e.getMessage());
            return ResponseEntity.badRequest().body(response);
        }
    }
    
    private void listDirectoryContents(File dir, Map<String, Object> response, String prefix) {
        File[] files = dir.listFiles();
        if (files != null) {
            for (File file : files) {
                String key = prefix + file.getName();
                if (file.isDirectory()) {
                    response.put(key + "/", "directory");
                    listDirectoryContents(file, response, key + "/");
                } else {
                    response.put(key, file.length() + " bytes");
                }
            }
        }
    }
}
